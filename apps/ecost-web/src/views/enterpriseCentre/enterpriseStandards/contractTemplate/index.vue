<template>
  <Page auto-content-height>
    <div class="bg-card flex h-full flex-col rounded-lg border p-2">
      <div class="flex-1 overflow-hidden">
        <vxe-grid ref="tableRef" v-bind="tableOptions" v-on="gridEvents">
          <template #top>
            <div class="flex items-center gap-4">
              <div class="mb-2">
                <ElButton
                  class="w-20"
                  type="primary"
                  size="small"
                  :disabled="!couldUpload"
                  @click="uploadContract"
                >
                  上传范本
                </ElButton>
              </div>
              <div class="mb-2">
                <ElButton
                  class="w-20"
                  :disabled="!couldPublish"
                  type="primary"
                  size="small"
                  @click="publish"
                >
                  {{
                    currentItem.versionStatus === versionStatus.PUBLISHED
                      ? '取消发布'
                      : '发布'
                  }}
                </ElButton>
              </div>
            </div>
          </template>
          <template #versionStatus="{ row }">
            <ElTag
              v-if="row.versionStatus"
              :type="
                row.versionStatus === versionStatus.PUBLISHED
                  ? 'success'
                  : row.versionStatus === versionStatus.NOUSEING
                    ? 'warning'
                    : 'primary'
              "
            >
              {{ getStatusLabel(row.versionStatus) }}
            </ElTag>
          </template>
          <template #seq="{ $rowIndex }">
            <div>{{ $rowIndex + 1 }}</div>
          </template>
          <template #name="{ row }">
            <div>
              <ElButton
                size="small"
                v-if="row.parentId"
                type="primary"
                link
                @click="openContract({ row })"
              >
                {{ row.name }}
              </ElButton>
              <span v-else>{{ row.name }}</span>
            </div>
          </template>
          <template #cnSeq="{ row }">
            <div>{{ row.parentId ? row.sort : numberToChinese(row.sort) }}</div>
          </template>
          <template #isMatching="{ row }">
            <div>
              <ElButton
                v-if="row.parentId"
                size="small"
                :type="row.isMatching === '全部匹配' ? 'primary' : 'default'"
                link
              >
                {{ row.isMatching }}
              </ElButton>
            </div>
          </template>
          <template #isMandatory="{ row }">
            <ElButton
              v-if="row.parentId"
              size="small"
              :type="row.isMandatory === '有' ? 'primary' : 'default'"
              link
            >
              {{ row.isMandatory }}
            </ElButton>
          </template>
          <template #compileDate="{ row }">
            <div v-if="row.parentId">
              {{ dayjs(row.compileDate).format('YYYY-MM-DD HH:mm:ss') }}
            </div>
            <div v-else></div>
          </template>

          <template #referNums="{ row }">
            <div v-if="row.parentId">{{ row.referNums }}</div>
          </template>
        </vxe-grid>
      </div>
      <ContractWord
        ref="contractWordEl"
        v-model:visible="drawerVisible"
        :editable="contractEditable"
        @submit="submit"
        @refresh="refreshData"
      />
      <input
        ref="fileEl"
        type="file"
        class="hidden"
        accept=".docx,application/vnd.openxmlformats-officedocument.wordprocessingml.document"
        @change="handleFileChange"
      />
    </div>
  </Page>
</template>

<script lang="ts" setup>
import { onBeforeMount, reactive, ref } from 'vue';

import { Page } from '@vben/common-ui';

import dayjs from 'dayjs';
import { ElButton, ElMessage, ElMessageBox, ElTag } from 'element-plus';

import { fileCloudUpload, getWpsFileId } from '#/api/couldApi';
import {
  addContractStandard,
  delContractStandard,
  editContractStandard,
  editContractStandardStatus,
  getContractStandardList,
  getMandatoryTermList,
  getStandardFieldRuleList,
  moveContractStandard,
} from '#/api/enterpriseCenter/contractTemplate/contractTemplate';
import { numberToChinese } from '#/utils/common';

import ContractWord from './components/ContractEditor/index.vue';

defineOptions({
  name: 'ContractTemplate',
});

enum versionStatus {
  NOUSEING = 'NOUSEING', // 停用
  PUBLISHED = 'PUBLISHED', //  发布
  UNPUBLISHED = 'UNPUBLISHED', // 未发布
}

const getStatusLabel = (status: versionStatus) => {
  const statusMap = {
    [versionStatus.NOUSEING]: '停用',
    [versionStatus.PUBLISHED]: '已发布',
    [versionStatus.UNPUBLISHED]: '未发布',
  };
  return statusMap[status] || '未知状态';
};

const contractEditable = ref(true); // 合约是否可编辑
const contractWordEl = ref();
const drawerVisible = ref(false); // 合同范本侧抽屉是否展示
const couldUpload = ref(false);
const couldPublish = ref(false);
const fileEl = ref();
const tableRef = ref();
const addOrEditForm = ref({
  // 新增/编辑表单
  id: null,
  classify: '',
  name: '',
  remark: '',

  fileKey: '',
  fileName: '',
  fileContentType: '',
  fileSize: '',
  fileExt: '',
});

const currentItem = ref({
  id: '',
  classify: '',
  versionStatus: versionStatus.UNPUBLISHED,
}); // 当前点击项
const fieldRule = ref([]);
const mandatoryTerm = ref([]);
// 表格配置
const columns = [
  {
    file: 'seq',
    title: '',
    width: '60',
    slots: {
      default: 'seq',
    },
    treeNode: true,
  },
  {
    field: 'cnSeq',
    title: '序号',
    width: '60',
    slots: {
      default: 'cnSeq',
    },
  },
  {
    field: 'name',
    title: '合同类型/范本名称',
    minWidth: '150',
    slots: {
      default: 'name',
    },
  },
  {
    field: 'remark',
    title: '范本说明',
    width: '280',
  },
  {
    field: 'compileDate',
    title: '编制日期',
    width: '180',
    slots: {
      default: 'compileDate',
    },
  },
  {
    field: 'compileBy',
    title: '编制人',
    width: '180',
  },
  {
    field: 'isMatching',
    title: '字段匹配',
    width: '120',
    slots: {
      default: 'isMatching',
    },
  },
  {
    field: 'isMandatory',
    title: '强制性条款',
    width: '120',
    slots: {
      default: 'isMandatory',
    },
  },
  {
    field: 'versionStatus',
    title: '发布状态',
    width: '180',
    slots: {
      default: 'versionStatus',
    },
  },
  {
    field: 'referNums',
    title: '被引用数',
    width: '100',
    slots: {
      default: 'referNums',
    },
  },
];
const tableOptions = reactive<any>({
  size: 'mini',
  height: '100%',
  autoresize: true,
  border: true,
  align: 'center',
  showOverflow: true,
  keepSource: true,
  headerRowClassName: 'bg-sky-200 text-black-800',
  loading: true,
  rowClassName: ({ row }: any) => {
    return row.parentId ? '' : 'bg-gray-100';
  },
  columnConfig: {
    resizable: true,
  },
  mouseConfig: {
    selected: true,
  },
  treeConfig: {
    rowField: 'id',
    parentField: 'parentId',
    transform: true,
    expandAll: true,
  },
  rowConfig: {
    isCurrent: true,
  },
  menuConfig: {
    body: {
      options: [
        [
          {
            code: 'DELETE_ROW',
            name: '删除行',
            prefixConfig: { icon: 'vxe-icon-delete-fill' },
            disabled: false,
          },
          {
            code: 'MOVE_UP',
            name: '上移',
            prefixConfig: { icon: 'vxe-icon-arrows-up' },
            disabled: false,
          },
          {
            code: 'MOVE_DOWN',
            name: '下移',
            prefixConfig: { icon: 'vxe-icon-arrows-down' },
            disabled: false,
          },
        ],
      ],
    },
    visibleMethod: ({ options, row }: any) => {
      if (!row.parentId || row.versionStatus === versionStatus.PUBLISHED) {
        options[0].forEach((item: any) => {
          item.disabled = true;
        });
        return true;
      }
      const targetParentId = row.parentId;
      const tableData = tableOptions.data.filter(
        (v: any) => v.parentId === targetParentId,
      );
      const targetIdx = tableData.findIndex((v: any) => v.id === row.id);
      options[0].forEach((item: any) => {
        switch (item.code) {
          case 'MOVE_DOWN': {
            item.disabled = targetIdx === tableData.length - 1;
            break;
          }
          case 'MOVE_UP': {
            item.disabled = targetIdx === 0;
            break;
          }
          default: {
            item.disabled = false;
          }
        }
      });
      return true;
    },
  },
  columns,
  data: [],
});
// 表格事件
const gridEvents = {
  cellClick({ row }: { row: any }) {
    couldUpload.value = true;
    couldPublish.value = false;
    if (row.parentId) {
      couldPublish.value = true;
    }
    currentItem.value = row;
  },
  cellMenu({ row }: { row: any }) {
    couldUpload.value = true;
    couldPublish.value = false;
    if (row.parentId) {
      couldPublish.value = true;
    }
    const $grid = tableRef.value;
    if ($grid) {
      $grid.setCurrentRow(row);
    }
    currentItem.value = row;
  },
  async menuClick({ menu, row }: { menu: any; row: any }) {
    if (menu.code === 'DELETE_ROW') {
      const { id } = row;
      ElMessageBox.confirm('你确定要删除吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'error',
      }).then(async () => {
        const res = await delContractStandard(id);
        if (res) {
          refreshData();
          ElMessage.success('删除成功');
        }
      });
    }
    if (menu.code === 'MOVE_UP') {
      const { id } = row;
      const moveType = 'up';
      moveContract({
        id,
        moveType,
      });
    }
    if (menu.code === 'MOVE_DOWN') {
      const { id } = row;
      const moveType = 'down';
      moveContract({
        id,
        moveType,
      });
    }
  },
};
// 上传范本
const uploadContract = () => {
  // 清空文件数据
  fileEl.value.value = '';
  // 清空强制条款数据 和 字段数据
  fieldRule.value = [];
  mandatoryTerm.value = [];
  // 清空表单数据
  addOrEditForm.value.id = null;
  addOrEditForm.value.name = '';
  addOrEditForm.value.remark = '';
  addOrEditForm.value.fileKey = '';
  addOrEditForm.value.fileName = '';
  addOrEditForm.value.fileContentType = '';
  addOrEditForm.value.fileSize = '';
  addOrEditForm.value.fileExt = '';

  // currentItem.value = tableRef.value.getCurrentRecord(); // 获取拿到的当前列数据
  fileEl.value.click();
};
//  获取文件
const handleFileChange = async () => {
  const file = fileEl.value.files[0];
  if (!file) return;
  const fileName = file.name;
  const idx = fileName.lastIndexOf('.');
  const ext = idx === -1 ? '' : fileName.slice(idx + 1);
  const name = idx === -1 ? '' : fileName.slice(0, idx);
  tableOptions.loading = true;
  // 调用上传接口 获取fileKey
  const fileRes = await fileCloudUpload(file,{ isUpload:true });
  if (!fileRes) return;
  // 保存字段
  addOrEditForm.value.fileKey = fileRes.fileKey;
  addOrEditForm.value.fileName = file.name;
  addOrEditForm.value.fileContentType = file.type;
  addOrEditForm.value.fileSize = String(file.size);

  addOrEditForm.value.fileExt = ext;

  contractEditable.value = true;
  // 调用获取fileId接口

  const currentTimestamp = Math.floor(Date.now() / 1000);
  // 第一次用这个时间, 第二次用服务端时间

  const params = {
    productCode: 'ecost',
    fileKey: addOrEditForm.value.fileKey,
    name: addOrEditForm.value.fileName,
    fileContentType: addOrEditForm.value.fileContentType,
    size: Number(addOrEditForm.value.fileSize),
    create_time: currentTimestamp,
    modify_time: currentTimestamp,
  };
  const res = await getWpsFileId(params);
  const fileId = res.fileId;
  // 打开wps
  tableOptions.loading = false;
  if (res) {
    const form = {
      id: null,
      classify: currentItem.value.classify,
      name,
      remark: '',
      fileId,
    };
    contractWordEl.value.init({
      form,
      fieldRuleData: fieldRule.value,
      mandatoryTermData: mandatoryTerm.value,
    });
    drawerVisible.value = true;
  }
};
// const addContractTemplate(){

// }
// 提交范本
async function submit(data: any) {
  const id = addOrEditForm.value.id;
  if (id) {
    // 修改
    const params = {
      name: data.name, // 范本名称
      remark: data.remark, // 范本说明

      fileExt: addOrEditForm.value.fileExt,
      fileKey: addOrEditForm.value.fileKey,
      fileName: addOrEditForm.value.fileName,
      fileContentType: addOrEditForm.value.fileContentType,
      fileSize: addOrEditForm.value.fileSize,
    };
    if (typeof id !== 'string') {
      ElMessage.error('无效的操作,合同范本ID不合法');
      return;
    }
    const res = await editContractStandard(id, params);
    if (res) {
      ElMessage.success('修改成功');
      refreshData();
    }
  } else {
    // 新增
    const params = {
      classify: currentItem.value.classify, // 合同范本分类
      name: data.name, // 范本名称
      remark: data.remark, // 范本说明

      fileExt: addOrEditForm.value.fileExt,
      fileKey: addOrEditForm.value.fileKey,
      fileName: addOrEditForm.value.fileName,
      fileContentType: addOrEditForm.value.fileContentType,
      fileSize: addOrEditForm.value.fileSize,
    };
    const res = await addContractStandard(params);
    if (res) {
      ElMessage.success('提交成功');
      refreshData();
    }
  }
  drawerVisible.value = false;
}
// 新增字段规则
// 新增强制条款
// await addMandatoryTerm({
//   name,
//   content: '测试' + dayjs(new Date()).format('YYYY-MM-DD HH:mm:ss'),
//   contractTemplateId: id
// })
// async function addFiled(row:any){
//   if(!addOrEditForm.value.id){
//     ElMessage.error('无效的操作,合同范本ID不合法');
//     return
//   }
//   const params = {
//     coord: String(row.idx),
//     fieldRuleId: row.id,
//     contractTemplateId: addOrEditForm.value.id
//   }
//   const res = await addContractStandardFieldRule(params)

//   if(res){
//     ElMessage.success('添加成功')
//   }
// }
// 打开范本
const openContract = async ({ row }: any) => {
  tableOptions.loading = true;
  const {
    id,
    name,
    remark,
    fileKey,
    fileContentType,
    fileSize,
    fileName,
    createAt,
    updateAt,
  } = row;

  contractEditable.value = row.versionStatus !== versionStatus.PUBLISHED;

  addOrEditForm.value.id = id;
  addOrEditForm.value.name = name;
  addOrEditForm.value.remark = remark;
  addOrEditForm.value.fileKey = fileKey;
  addOrEditForm.value.fileContentType = fileContentType;
  addOrEditForm.value.fileSize = fileSize;
  addOrEditForm.value.fileName = fileName;
  // 获取fileId
  const create_time = dayjs(createAt).unix();
  const modify_time = dayjs(updateAt).unix();
  const params = {
    productCode: 'ecost',
    fileKey: addOrEditForm.value.fileKey,
    name: addOrEditForm.value.fileName,
    fileContentType: addOrEditForm.value.fileContentType,
    size: Number(addOrEditForm.value.fileSize),
    create_time,
    modify_time,
  };
  const res = await getWpsFileId(params);
  const fileId = res.fileId;
  // 获取字段规则
  const fieldRuleRes = await getStandardFieldRuleList({
    name: '',
    contractTemplateId: id,
  });
  fieldRule.value = fieldRuleRes;
  // 获取强制条款
  const mandatoryTermRes = await getMandatoryTermList({
    contractTemplateId: id,
  });
  mandatoryTerm.value = mandatoryTermRes;
  const form = {
    id,
    name,
    remark,
    fileId,
  };
  contractWordEl.value.init({
    form,
    fieldRuleData: fieldRule.value,
    mandatoryTermData: mandatoryTerm.value,
  });
  tableOptions.loading = false;
  drawerVisible.value = true;
};

// 获取合同范本列表
async function getList() {
  tableOptions.loading = true;
  const params = {};
  const res = await getContractStandardList(params);
  tableOptions.loading = false;
  tableOptions.data = res;
}
// 移动合同范本
type moveContractData = {
  id: string;
  moveType: 'down' | 'up';
};
async function moveContract(params: moveContractData) {
  const res = await moveContractStandard(params);
  if (res) {
    refreshData();
  }
}
// 发布或取消发布范本
const publish = async () => {
  const params = {
    id: currentItem.value.id,
    versionStatus:
      currentItem.value.versionStatus === versionStatus.PUBLISHED
        ? versionStatus.UNPUBLISHED
        : versionStatus.PUBLISHED,
  };
  const res = await editContractStandardStatus(params);

  if (res) {
    refreshData();
    ElMessage.success(
      currentItem.value.versionStatus === versionStatus.PUBLISHED
        ? '取消发布成功'
        : '发布成功',
    );
  }
};
// 刷新
async function refreshData() {
  await getList();
  const activeRow = tableOptions.data.find(
    (v: any) => v.id === currentItem.value.id,
  );
  const $grid = tableRef.value;
  if (activeRow && $grid) {
    $grid.setCurrentRow(activeRow);
    currentItem.value = activeRow;
  }
  tableRef.value.setAllTreeExpand(true);
}
// 初始化
async function init() {
  await getList();
}

// 渲染前
onBeforeMount(async () => {
  init();
});
</script>

<style lang="scss">
.vxe-cell--tree-node,
.vxe-tree-cell {
  padding-left: 0 !important;
}
</style>
