<template>
  <Page class="ml-4 mt-4 h-full rounded bg-white">
    <div class="flex h-full">
      <div class="area-left h-full w-[15%] pr-2">
        <TimeSelect @select="timeSelect" />
      </div>
      <div class="area-right h-full w-[85%]">
        <vxe-grid ref="tableRef" v-bind="tableOptions" v-on="tableEvents">
          <template #top>
            <div class="flex items-center justify-between gap-4">
              <div class="mb-2 flex items-center gap-2">
                <ElButton type="primary" size="small" @click="addData">
                  新增
                </ElButton>
                <ElButton type="primary" size="small"> 提交 </ElButton>
                <!-- <ElButton type="primary" size="small" disabled>
                  发起审核
                </ElButton>
                <ElButton type="primary" size="small" disabled>
                  查看流程
                </ElButton>
                <div class="ml-3 mr-3">
                  <ElDropdown size="small" @command="handleDropDownItem">
                    <ElButton type="primary" size="small" disabled>
                      批量导入
                      <IconifyIcon icon="bi:arrow-down" />
                    </ElButton>
                    <template #dropdown>
                      <ElDropdownMenu>
                        <ElDropdownItem command="download" disabled>
                          模版下载
                        </ElDropdownItem>
                      </ElDropdownMenu>
                    </template>
                  </ElDropdown>
                </div> -->
                <ElButton type="primary" size="small"> 调拨 </ElButton>
                <ElButton type="primary" size="small"> 筛选 </ElButton>
              </div>
              <div class="flex items-center gap-4">
                <div class="mb-2">
                  <ElButton type="default" size="small"> 导出单据 </ElButton>
                </div>
                <div class="mb-2">
                  <ElButton type="default" size="small"> 导出列表 </ElButton>
                </div>
              </div>
            </div>
          </template>

          <template #seq="{ $rowIndex }">
            <div>{{ $rowIndex + 1 }}</div>
          </template>
          <template #code="{ row }">
            <div>
              <ElButton
                size="small"
                type="primary"
                link
                @click="openDetail(row)"
              >
                {{ row.code }}
              </ElButton>
            </div>
          </template>

          <template #purchaseType="{ row }">
            <div>
              {{ getPurchaseTypeLabel(row.purchaseType) }}
            </div>
          </template>

          <template #materialReceiptStatus="{ row }">
            <div
              :class="{
                'text-green-500':
                  row.materialReceiptStatus === MaterialReceiptStatus.RECEIVED,
                'text-red-500':
                  row.materialReceiptStatus ===
                  MaterialReceiptStatus.UN_RECEIVED,
              }"
            >
              {{ getMaterialReceiptStatusLabel(row.materialReceiptStatus) }}
            </div>
          </template>

          <template #createAt="{ row }">
            <div>
              {{
                dayjs(`${row.year}/${row.month}/${row.day}`).format(
                  'YYYY-MM-DD',
                )
              }}
            </div>
          </template>

          <template #submitStatus="{ row }">
            <div
              :class="{
                'text-green-500': row.submitStatus === SubmitStatus.SUBMITTED,
                'text-red-500': row.submitStatus === SubmitStatus.PENDING,
              }"
            >
              {{ getSubmitStatusLabel(row.submitStatus) }}
            </div>
          </template>
          <template #auditStatus="{ row }">
            <div
              :class="{
                'text-red-500': row.auditStatus === AuditStatus.PENDING,
                'text-orange-500':
                  row.auditStatus === AuditStatus.AUDITING ||
                  row.auditStatus === AuditStatus.REJECTED,
                'text-green-500': row.auditStatus === AuditStatus.APPROVED,
              }"
            >
              {{ getAuditStatusLabel(row.auditStatus) }}
            </div>
          </template>
          <template #qrcode="{ row }">
            <ElPopover
              placement="left"
              trigger="click"
              v-if="row.submitStatus === SubmitStatus.SUBMITTED"
            >
              <div>
                <QrcodeVue :value="row.code" :size="120" />
              </div>
              <template #reference>
                <div class="flex justify-center">
                  <QrcodeVue :value="row.code" :size="28" />
                </div>
              </template>
            </ElPopover>
          </template>
        </vxe-grid>
      </div>
    </div>
    <EditDrawer
      v-model:visible="drawerVisible"
      :info-data="infoData"
      :editable="editable"
      @move="contractSelectMove"
    />
  </Page>
</template>

<script lang="ts" setup>
import { onBeforeMount, reactive, ref } from 'vue';

import { Page } from '@vben/common-ui';

import dayjs from 'dayjs';
import { ElButton, ElMessage, ElMessageBox, ElPopover } from 'element-plus';
import QrcodeVue from 'qrcode.vue';

import {
  addInspectionBill,
  delInspectionBill,
  getInspectionBillList,
} from '#/api/enterpriseCenter/materialEntryCheck/materialEntryCheck';
import { changeReviewStatus } from '#/api/enterpriseCenter/materialManagement/materialContract';
import { setCurrentRow, vxeBaseConfig } from '#/utils/vxeTool';

import EditDrawer from './components/EditDrawer.vue';
import TimeSelect from './components/TimeSelect.vue';

defineOptions({
  name: 'MaterialEntryCheck',
});

// 收料状态
const MaterialReceiptStatus = {
  UN_RECEIVED: 'UN_RECEIVED', // 未收料
  RECEIVED: 'RECEIVED', // 已收料
  PARTIAL_RECEIVED: 'PARTIAL_RECEIVED', // 部分收料
} as const;
type MaterialReceiptStatusType =
  (typeof MaterialReceiptStatus)[keyof typeof MaterialReceiptStatus];

function getMaterialReceiptStatusLabel(status: MaterialReceiptStatusType) {
  const map = {
    [MaterialReceiptStatus.UN_RECEIVED]: '未收料',
    [MaterialReceiptStatus.RECEIVED]: '已收料',
    [MaterialReceiptStatus.PARTIAL_RECEIVED]: '部分收料',
  };
  return map[status] || '未知状态';
}

// 采购类型
const PurchaseType = {
  SELF_PURCHASE: 'SELF_PURCHASE', // 自采
  CENTRALIZED_PURCHASE: 'CENTRALIZED_PURCHASE', // 集采
  PARTY_A_DIRECTED: 'PARTY_A_DIRECTED', // 甲供
  PARTY_A_SUPPLIED: 'PARTY_A_SUPPLIED', // 甲指
  TRANSFER_IN: 'TRANSFER_IN', // 调拨
} as const;
type PurchaseTypeEnum = (typeof PurchaseType)[keyof typeof PurchaseType];

function getPurchaseTypeLabel(status: PurchaseTypeEnum) {
  const map = {
    [PurchaseType.SELF_PURCHASE]: '自采',
    [PurchaseType.CENTRALIZED_PURCHASE]: '集采',
    [PurchaseType.PARTY_A_DIRECTED]: '甲供',
    [PurchaseType.PARTY_A_SUPPLIED]: '甲指',
    [PurchaseType.TRANSFER_IN]: '调拨',
  };
  return map[status] || '未知状态';
}

// 提交状态
const SubmitStatus = {
  PENDING: 'PENDING', // 未提交
  SUBMITTED: 'SUBMITTED', // 已提交
} as const;
type SubmitStatusType = (typeof SubmitStatus)[keyof typeof SubmitStatus];

function getSubmitStatusLabel(status: SubmitStatusType) {
  const map = {
    [SubmitStatus.PENDING]: '未提交',
    [SubmitStatus.SUBMITTED]: '已提交',
  };
  return map[status] || '未知状态';
}

// 审核状态
const AuditStatus = {
  PENDING: 'PENDING', // 待审核
  AUDITING: 'AUDITING', // 审批中
  APPROVED: 'APPROVED', // 审核通过
  REJECTED: 'REJECTED', // 审核拒绝
} as const;
type AuditStatusType = (typeof AuditStatus)[keyof typeof AuditStatus];
function getAuditStatusLabel(status: AuditStatusType) {
  const map = {
    [AuditStatus.PENDING]: '待审核',
    [AuditStatus.AUDITING]: '审批中',
    [AuditStatus.APPROVED]: '已审批',
    [AuditStatus.REJECTED]: '被退回',
  };
  return map[status] || '未知状态';
}
const asuditStatusOption = [
  { label: '待审批', value: AuditStatus.PENDING },
  { label: '审核中', value: AuditStatus.AUDITING },
  { label: '审批通过', value: AuditStatus.APPROVED },
  { label: '驳回', value: AuditStatus.REJECTED },
];

// 新增弹窗是否展示
const addFromDialogVisible = ref(false);

// 侧抽屉数据
const drawerVisible = ref(false);
const infoData = ref();
const editable = ref(true); // 是否可编辑

const tableRef = ref();

const addOrEditContractForm = ref<any>({
  // 新增/编辑表单
  id: null,
  parentId: null,

  name: '',
  code: '',
  partyA: '',
  partyB: '',
  contractTemplateId: '',
  partyBType: '',
  proposedStatus: '',
});

// 当前选择的时间
const currentTimeItem = ref();

// 当前点击项
const currentItem = ref<any>();

// 表格配置
const columns = [
  {
    file: 'seq',
    title: '',
    width: '60',
    slots: {
      default: 'seq',
    },
    treeNode: true,
  },
  {
    field: 'materialReceiptStatus',
    title: '收料状态',
    width: '150',
    slots: {
      default: 'materialReceiptStatus',
    },
  },
  {
    field: 'code',
    title: '单据编码',
    width: '150',
    slots: {
      default: 'code',
    },
  },
  {
    field: 'purchaseType',
    title: '采购类型',
    minWidth: '200',
    slots: {
      default: 'purchaseType',
    },
  },
  {
    field: 'supplierName',
    title: '供应商名称',
    width: '200',
  },
  {
    field: 'contractName',
    title: '合同名称',
    width: '200',
  },
  {
    field: 'materialCategories',
    title: '材料类别',
    width: '200',
  },
  {
    field: 'creator',
    title: '编制人',
    width: '150',
  },
  {
    field: 'createAt',
    title: '进场日期',
    width: '150',
    slots: {
      default: 'createAt',
    },
  },
  {
    field: 'submitStatus',
    title: '提交状态',
    width: '150',
    slots: {
      default: 'submitStatus',
    },
  },
  {
    field: 'auditStatus',
    title: '审批状态',
    width: '150',
    editRender: {
      name: 'VxeSelect',
      options: asuditStatusOption,
    },
    slots: {
      default: 'auditStatus',
    },
  },
  {
    title: '二维码',
    width: '150',
    slots: {
      default: 'qrcode',
    },
  },
];
const tableOptions = reactive<any>({
  ...vxeBaseConfig,
  treeConfig: {
    rowField: 'id',
    parentField: 'parentId',
    transform: true,
    expandAll: true,
  },
  menuConfig: {
    body: {
      options: [
        [
          {
            code: 'DELETE_ROW',
            name: '删除',
            prefixConfig: { icon: 'vxe-icon-delete-fill' },
            disabled: false,
          },
        ],
      ],
    },
    visibleMethod: ({ options }: any) => {
      options[0].forEach((item: any) => {
        switch (item.code) {
          default: {
            item.disabled = false;
          }
        }
      });
      return true;
    },
  },
  editConfig: {
    trigger: 'dblclick',
    mode: 'cell',
    showStatus: true,
    showIcon: true,
    beforeEditMethod({ row }: any) {
      // 版本数据已启用无法进行编辑
      if (row.submitStatus === SubmitStatus.PENDING) {
        ElMessage.warning('请先提交数据');
        return false;
      }
      return true;
    },
  },
  columns,
  data: [],
});
// 表格事件
const tableEvents = {
  cellClick({ row }: { row: any }) {
    currentItem.value = row;
    editable.value = row.submitStatus === SubmitStatus.PENDING;
  },
  cellMenu({ row }: { row: any }) {
    currentItem.value = row;
    editable.value = row.submitStatus === SubmitStatus.PENDING;
    setCurrentRow(tableOptions.data, tableRef.value, currentItem.value);
  },
  async menuClick({ menu, row }: { menu: any; row: any }) {
    if (menu.code === 'DELETE_ROW') {
      const { id } = row;
      ElMessageBox.confirm('你确定要删除吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'error',
      }).then(async () => {
        const res = await delInspectionBill(id);
        if (res) {
          refreshData();
          ElMessage.success('删除成功');
        }
      });
    }

    if (menu.code === 'EDIT_ROW') {
      const {
        id,
        parentId,
        name,
        code,
        partyA,
        partyB,
        contractTemplateId,
        partyBType,
        proposedStatus,
      } = row;
      addOrEditContractForm.value = row.parentId
        ? {
            id,
            parentId,
            name,
            code,
            contractTemplateId,
          }
        : {
            id,
            parentId,
            name,
            code,
            partyA,
            partyB,
            contractTemplateId,
            partyBType,
            proposedStatus,
          };

      addFromDialogVisible.value = true;
    }
  },
  // 完成编辑
  async editClosed({ row, column }: any) {
    if (column.field === 'auditStatus') {
      const id = row.id;
      const item = asuditStatusOption.find(
        (item) => item.value === row.auditStatus,
      );
      ElMessageBox.confirm(`你确定要${item?.label}吗?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(async () => {
          const params = {
            auditStatus: row.auditStatus,
          };
          const res = await changeReviewStatus(id, params);
          if (res) {
            refreshData();
            ElMessage.success(`${item?.label}成功`);
          }
        })
        .catch(async () => {
          refreshData();
        });
    }
  },
};

// 新增数据
async function addData() {
  const res = await addInspectionBill();
  const itemData = {
    id: res.id,
    orgName: res.orgName,
    auditStatus: res.auditStatus,
    code: res.code,
    creator: res.creator,
    day: res.day,
    month: res.month,
    year: res.year,
    purchaseType: res.purchaseType,
    submitStatus: res.submitStatus,
    materialReceiptStatus: res.materialReceiptStatus,

    supplierId: null,
    supplierName: null,
    contractId: null,
    contractName: null,
  };
  infoData.value = itemData;
  currentItem.value = itemData;
  await refreshData();

  drawerVisible.value = true;
}

// 打开详情
async function openDetail(row: any) {
  infoData.value = {
    id: row.id,
    orgName: row.orgName,
    auditStatus: row.auditStatus,
    code: row.code,
    creator: row.creator,
    day: row.day,
    month: row.month,
    year: row.year,

    purchaseType: row.purchaseType,
    submitStatus: row.submitStatus,
    materialReceiptStatus: row.materialReceiptStatus,

    supplierId: null,
    supplierName: null,
    contractId: null,
    contractName: null,
  };
  drawerVisible.value = true;
}

// 合同选中移动
async function contractSelectMove(move: number) {
  const $grid = tableRef.value;
  const tableData = $grid.getTableData();
  const currentRow = $grid.getCurrentRecord();
  const targetIdx = $grid.getVTRowIndex(currentRow);
  if (move > 0 && targetIdx < tableData.visibleData.length - 1) {
    // 下一个
    const targetItem = tableData.visibleData[targetIdx + 1];
    tableEvents.cellClick({ row: targetItem });
    $grid.setCurrentRow(targetItem);
    openContract({ row: targetItem });
  }
  if (move < 0 && targetIdx > 0) {
    // 上一个
    const targetItem = tableData.visibleData[targetIdx - 1];
    tableEvents.cellClick({ row: targetItem });
    $grid.setCurrentRow(targetItem);
    openContract({ row: targetItem });
  }
}

// 刷新
async function refreshData() {
  await getList();
  setCurrentRow(tableOptions.data, tableRef.value, currentItem.value);
}

// 时间选择
async function timeSelect(row: any) {
  currentTimeItem.value = row;

  await getList();
}

// 初始化
async function init() {
  await getList();
}
// 获取物资进场验收数据
async function getList() {
  tableOptions.loading = true;

  const params: any = {};
  if (currentTimeItem.value?.year) {
    params.year = currentTimeItem.value.year;
  }
  if (currentTimeItem.value?.month) {
    params.month = currentTimeItem.value.month;
  }
  if (currentTimeItem.value?.day) {
    params.day = currentTimeItem.value.day;
  }
  const res = await getInspectionBillList(params);

  tableOptions.data = res;
  tableOptions.loading = false;
}
// function handleDropDownItem(command: string) {
//   if (command === 'download') {
//     // TODO 模版下载
//     downloadLocalFile('/file/费用字典发布.xlsx', '费用字典发布.xlsx');
//   }
// }
// 渲染前
onBeforeMount(async () => {
  init();
});
</script>

<style lang="scss">
.vxe-cell--tree-node,
.vxe-tree-cell {
  padding-left: 0 !important;
}
</style>
