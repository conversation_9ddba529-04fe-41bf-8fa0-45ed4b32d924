<template>
  <VxeGrid ref="tableRef" v-bind="tableOptions" v-on="tableEvents">
    <template #top>
      <ElDatePicker
        class="mb-2"
        v-model="time"
        type="date"
        placeholder="请选择日期"
        size="default"
      />
    </template>
    <template #seq="{ $rowIndex }">
      <div>{{ $rowIndex + 1 }}</div>
    </template>
    <template #name="{ row }">
      <div class="font-bold">
        {{ `${row.name} (${row.count})` }}
      </div>
    </template>
  </VxeGrid>
</template>

<script setup lang="ts">
import { onBeforeMount, reactive, ref } from 'vue';

import dayjs from 'dayjs';
import { ElDatePicker } from 'element-plus';

import { getTimeList } from '#/api/enterpriseCenter/materialEntryCheck/materialEntryCheck';
import { setCurrentRow, vxeBaseConfig } from '#/utils/vxeTool';

const emit = defineEmits<{
  (e: 'select', row: any): void;
}>();

const staticItem = {
  id: '',
  name: '全部',
  count: 0,
  disabled: true,
  editable: true,
};
const columns = [
  {
    field: 'name',
    title: '时间',
    treeNode: true,
    slots: {
      default: 'name',
    },
  },
];

const tableRef = ref();
const currentItem = ref({
  id: '',
  name: '全部',
  count: 0,
  year: null,
  month: null,
  day: null,
  disabled: true,
  editable: true,
});
const tableOptions = reactive<any>({
  ...vxeBaseConfig,
  columns,
  data: [],
});
const tableEvents = {
  cellClick({ row }: any) {
    emit('select', row);
  },
};
const time = ref();
async function getList() {
  tableOptions.loading = true;
  const res = await getTimeList();
  tableOptions.loading = false;
  let totalCount = 0;
  const resData = res.map((item: any) => {
    if (item.parentId) {
      totalCount += item.count;
    }

    let name;
    if (item.parentId) {
      const time = `${item.year}/${item.month}/${item.day}`;
      name = dayjs(time).format('DD日');
    } else {
      const time = `${item.year}/${item.month}`;
      name = dayjs(time).format('YYYY年M月');
    }
    return {
      ...item,
      name,
    };
  });
  staticItem.count = totalCount;
  tableOptions.data = [staticItem, ...resData];
}

async function init() {
  await getList();

  setCurrentRow(tableOptions.data, tableRef.value, currentItem.value);
}

onBeforeMount(async () => {
  await init();
});
</script>
