<template>
  <div class="contracteditor flex h-full w-full" v-loading="contentLoading">
    <!-- 左边区域 -->
    <div class="area-left flex h-full w-[70%] flex-col">
      <WpsWordEditor
        ref="wpsEditorEl"
        :options="wpsOptions"
        @control-select="controlSelectChange"
        @end="wpsInitEnd"
      />
    </div>
    <div class="area-right flex w-[30%] flex-col p-4">
      <div
        class="search-area item-center box-border-box mb-4 flex h-[32px] w-full justify-between"
      >
        <div class="btn-group flex items-center gap-4">
          <ElButton type="primary" size="small" link @click="expand('open')">
            <div class="text-sm">展开</div>
          </ElButton>
          <ElButton type="primary" size="small" link @click="expand('close')">
            <div class="text-sm">折叠</div>
          </ElButton>
        </div>
        <div class="flex items-center gap-2 pr-2">
          <ElButton type="primary" size="small" @click="validate">
            {{ isFilterEmpty ? '显示全部' : '显示未填字段' }}
          </ElButton>
          <ElButton
            type="primary"
            size="small"
            :disabled="!editable"
            @click="save"
          >
            保存
          </ElButton>
        </div>
      </div>
      <div class="filed-table-container mb-2 w-full flex-1">
        <vxe-grid ref="tableRef" v-bind="tableOptions" v-on="tableEvents">
          <template #top></template>
          <template #name="{ row }">
            <div class="flex justify-center gap-1">
              <div v-if="row.isRequired" class="mt-[-4px] text-red-500">*</div>
              <div>{{ row.name }}</div>
            </div>
          </template>
          <template #enumValue="{ row }">
            <div v-if="row.parentId !== null">
              <div v-if="row.fieldType === 'TEXT'">
                <vxe-input :disabled="row.isUpdate" v-model="row.value" />
              </div>
              <div v-if="row.fieldType === 'NUMBER'">
                <vxe-number-input
                  :disabled="row.isUpdate"
                  v-model="row.value"
                  type="integer"
                />
              </div>
              <div v-if="row.fieldType === 'PERCENT'">
                <vxe-select
                  v-if="row.code === '增值税税率'"
                  :disabled="row.isUpdate"
                  v-model="row.value"
                  :options="row.enumValueOption"
                />
                <vxe-number-input
                  v-else
                  :disabled="row.isUpdate"
                  v-model="row.value"
                  type="integer"
                />
              </div>
              <div v-if="row.fieldType === 'ENUM'">
                <vxe-select
                  :disabled="row.isUpdate"
                  v-model="row.value"
                  :options="row.enumValueOption"
                />
              </div>
              <div v-if="row.fieldType === 'DATE'">
                <vxe-date-picker :disabled="row.isUpdate" v-model="row.value" />
              </div>
              <div v-if="row.fieldType === 'TABLE'">
                <ElTag
                  v-if="row.value.detailList.length > 0"
                  type="primary"
                  size="small"
                >
                  已创建
                </ElTag>
                <ElTag v-else type="warning" size="small"> 未创建 </ElTag>
              </div>
            </div>
          </template>
        </vxe-grid>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import {
  computed,
  inject,
  nextTick,
  onMounted,
  reactive,
  ref,
  watch,
} from 'vue';

import { dayjs, ElButton, ElMessage, ElTag } from 'element-plus';
import _ from 'lodash';

import {
  addOrEditContractField,
  getContractFieldList,
} from '#/api/enterpriseCenter/materialManagement/materialContract';
import WpsWordEditor from '#/components/WpsWordEditor/index.vue';

import { ContractTemplateEnum } from './type';

defineOptions({
  name: 'ContractEditor',
});

const props = withDefaults(
  defineProps<{
    contractFormData: any;
    contractInfoData: any;
  }>(),
  {
    contractInfoData: {
      parentId: null,
      contractId: '',
      contractTemplateType: '',
    },
    contractFormData: {
      id: '',
      createTime: '',
      fileContentType: '',
      fileKey: '',
      fileName: '',

      fileSize: '',
      updateTime: '',

      parentId: null,
    },
  },
);
// 合同的信息
const contractInfo = ref(props.contractInfoData);
watch(
  () => props.contractInfoData,
  (nval) => {
    contractInfo.value = nval;
  },
);
// 是否可编辑
const editable: any = inject('editable');

const wpsOptions = ref({
  editable: editable.value,
  protectable: false,
});

const contractForm = ref(props.contractFormData);
const contentLoading = ref(true);

watch(
  () => props.contractFormData,
  (nval) => {
    contractForm.value = nval;
  },
);

// wps编辑器DOM
const wpsEditorEl = ref();

// 表格数据
const tableRef = ref();
const isFilterEmpty = ref(false);
const currentItem = ref();
const columns = [
  {
    field: 'name',
    title: '字段名称',
    minWidth: '180',
    slots: {
      default: 'name',
    },
    treeNode: true,
  },
  {
    field: 'enumValue',
    title: '枚举值',
    width: '200',
    slots: {
      default: 'enumValue',
    },
  },
];
const originalData = ref([]);
const tableOptions = reactive<any>({
  size: 'mini',
  height: '98%',
  border: true,
  align: 'center',
  showOverflow: true,
  keepSource: true,
  headerRowClassName: 'bg-sky-200 text-black-800',
  rowClassName: ({ row }: { row: { parentId: null | string } }) => {
    return row.parentId ? '' : 'bg-gray-100';
  },
  mouseConfig: {
    selected: true,
  },
  columnConfig: {
    resizable: true,
  },
  treeConfig: {
    rowField: 'id',
    parentField: 'parentId',
    transform: true,
    expandAll: true,
  },
  rowConfig: {
    isCurrent: true,
  },
  checkboxConfig: {
    labelField: 'name',
    highlight: true,
  },
  editConfig: {
    trigger: 'dblclick',
    mode: 'cell',
    showStatus: true,
    showIcon: true,
    beforeEditMethod({ row }: any) {
      // 版本数据已启用无法进行编辑
      if (row.id === '' || !row.id || !row.parentId) {
        return false;
      }
      return true;
    },
  },
  columns,
  data: [],
  editRules: {
    enumValue: [
      {
        validator({ row }: any) {
          if (row.parentId) {
            if (row.isRequired && (!row.value || row.value === '')) {
              return new Error('请填写数据!');
            }
          } else {
            return true;
          }
        },
      },
    ],
  },
});
const tableEvents = {
  cellClick({ row }: any) {
    currentItem.value = row;
  },
};
// 展开 / 关闭
const expand = (type: string) => {
  if (type === 'close') {
    tableRef.value.clearTreeExpand();
  } else {
    tableRef.value.setAllTreeExpand(true);
  }
};

// 获取字段列表
async function getFiledList() {
  tableOptions.loading = true;
  const res = await getContractFieldList(contractForm.value.id);
  const filedData = res.map((item: any) => {
    let enumValueOption = [];
    if (item.fieldType === 'ENUM') {
      const arr = item.enumValue.split(',');
      enumValueOption = arr.map((v: any) => {
        return {
          value: v,
          label: v,
        };
      });
    }
    if (item.fieldType === 'PERCENT' && item.code === '增值税税率') {
      const dataArr =
        item.enumValue && item.enumValue !== ''
          ? item.enumValue.split(',')
          : [];
      enumValueOption = dataArr.map((item: any) => {
        return {
          label: item,
          value: item,
        };
      });
    }
    return {
      ...item,
      enumValueOption,
    };
  });
  // 保存原始数据（深拷贝）
  originalData.value = _.cloneDeep(filedData);
  tableOptions.data = originalData.value;

  tableOptions.loading = false;

  setCurrentRow(currentItem.value?.id || '');
}

// 控件选择
async function controlSelectChange(mark: string) {
  setCurrentRow(mark, 'name');
}

async function init() {
  // 初始化weps编辑器
  const params = {
    createTime: contractForm.value.createTime,
    fileContentType: contractForm.value.fileContentType,
    fileKey: contractForm.value.fileKey,
    fileName: contractForm.value.fileName,

    fileSize: contractForm.value.fileSize,
    updateTime: contractForm.value.updateTime,
  };
  const options = {
    editable,
  };
  wpsEditorEl.value.init({ params, options });

  await getFiledList();
}

const displayedData = computed(() => {
  if (isFilterEmpty.value) {
    return originalData.value;
  }
  return originalData.value.filter((item: any) => {
    if (item.parentId === null) {
      return true;
    } else {
      let isEmptyTable = false;
      if (item.fieldType === 'TABLE') {
        isEmptyTable = item.value.detailList <= 0;
      }
      return item.value === null || item.value === '' || isEmptyTable;
    }
  });
});

async function validate() {
  // 更新表格视图数据
  tableOptions.data = displayedData.value;
  setCurrentRow(currentItem.value?.id || '');
  isFilterEmpty.value = !isFilterEmpty.value;
}

// 批量执行异步任务
async function runInBatches<T>(tasks: (() => Promise<T>)[], batchSize = 5) {
  const results: T[] = [];
  for (let i = 0; i < tasks.length; i += batchSize) {
    const batch = tasks.slice(i, i + batchSize);
    const batchResults = await Promise.all(batch.map((fn) => fn()));
    results.push(...batchResults);
  }
  return results;
}
const wxTableHeader = [
  '货物名称',
  '规格型号',
  '材料、性能参数等（或执行的技术质量标准）',
  '品牌或厂家',
  '计量单位',
  '不含税单价',
  '增值税额',
  '含税单价',
  '计划数量',
  '暂定含税总价（元）',
  '备注',
];
const wxTableBodyColumns = [
  'name',
  'specificationModel',
  'qualityStandard',
  'brand',
  'unit',
  'changePriceExcludingTax',
  'changeAddedTaxAmount',
  'changePriceIncludingTax',
  'changeQuantity',
  'changeTotalPriceIncludingTax',
  'remark',
];

const shTableHeader = [
  '货物名称',
  '规格型号',
  '计量单位',
  '不含税单价',
  '增值税额',
  '含税单价',
  '计划数量',
  '暂定含税总价（元）',
  '备注',
];
const shTableBodyColumns = [
  'name',
  'specificationModel',
  'unit',
  'changePriceExcludingTax',
  'changeAddedTaxAmount',
  'changePriceIncludingTax',
  'changeQuantity',
  'changeTotalPriceIncludingTax',
  'remark',
];

const zpTableHeader = [
  '租赁物资名称',
  '规格型号',
  '计量单位',
  '不含税单价',
  '增值税额',
  '含税单价',
  '暂定数量',
  '暂定租赁天数',
  '备注',
];
const zpTableBodyColumns = [
  'name',
  'specificationModel',
  'unit',
  'changePriceExcludingTax',
  'changeAddedTaxAmount',
  'changePriceIncludingTax',
  'changeProvisionalDays',
  'changeProvisionalDays',
  'remark',
];

async function save() {
  isFilterEmpty.value = false;
  contentLoading.value = true;

  const $grid = tableRef.value;
  if ($grid) {
    const errMsg = await $grid.validate(true);
    if (errMsg) {
      contentLoading.value = false;
    } else {
      const id = contractForm.value.id;
      const filterData = tableOptions.data.filter((v: any) => !!v.parentId);

      const totalDays = filterData.filter((v: any) =>
        ['合同计划完工时间', '合同计划开始时间'].includes(v.code),
      );
      const totalDaysVaue = totalDays.filter((v: any) => v.value);

      const ruleList = filterData.map((item: any) => {
        // 手动计算 计划总工期（日历天）
        if (
          item.code === '计划总工期（日历天）' &&
          totalDaysVaue.length === 2
        ) {
          const startTime = totalDaysVaue.find(
            (v: any) => v.code === '合同计划开始时间',
          );
          const endTime = totalDaysVaue.find(
            (v: any) => v.code === '合同计划完工时间',
          );

          const date1 = dayjs(startTime.value);
          const date2 = dayjs(endTime.value);

          item.value = date2.diff(date1, 'day') + 1;
        }

        return {
          contractTemplateFieldRuleId: item.id,
          code: item.code,
          value:
            item.fieldType === 'TABLE' || !item.value ? '' : String(item.value),
        };
      });
      const params = { ruleList };
      await addOrEditContractField(id, params);
      ElMessage.warning({
        message: '该操作执行时间较长,请稍等...',
        duration: 5000,
      });
      const contractTemplateType = contractInfo.value.contractTemplateType;

      let tableHeader: any[] = [];
      let tableBodyColumns: any[] = [];

      switch (contractTemplateType) {
        case ContractTemplateEnum.MATERIALS_COMMERCIAL_CONCRETE: {
          // 商品混凝土
          tableHeader = shTableHeader;
          tableBodyColumns = shTableBodyColumns;

          break;
        }
        case ContractTemplateEnum.MATERIALS_LEASING_TURNOVER: {
          // 租赁周转
          tableHeader = zpTableHeader;
          tableBodyColumns = zpTableBodyColumns;

          break;
        }
        case ContractTemplateEnum.MATERIALS_PURCHASING: {
          // 材料-物资采购
          tableHeader = wxTableHeader;
          tableBodyColumns = wxTableBodyColumns;

          break;
        }
        // No default
      }

      // 分批封装任务（每个变成一个函数）
      const taskFns: (() => Promise<void>)[] = filterData.map((item: any) => {
        return async () => {
          let value: any = item.name;
          if (item.fieldType === 'TABLE') {
            const tableBody = Array.isArray(item?.value?.detailList)
              ? item.value.detailList.map((row: any) =>
                  tableBodyColumns.map((key) => {
                    const value = row?.[key];
                    if (typeof value === 'number') {
                      return [
                        'changeAddedTaxAmount',
                        'changePriceExcludingTax',
                        'changePriceIncludingTax',
                        'changeTotalPriceIncludingTax',
                      ].includes(key)
                        ? value.toFixed(2)
                        : String(value);
                    } else {
                      return value ? String(value) : '';
                    }
                  }),
                )
              : [];
            const options = {
              rows: tableBody.length + 1,
              cols: tableBody.length <= 0 ? 0 : tableBody[0].length,
            };
            value = {
              header: tableHeader,
              body: tableBody,
              options,
            };
          } else if (item.fieldType === 'PERCENT') {
            if (item.code === '增值税税率') {
              if (!item.enumValue || item.enumValue === '') return;
              const strArr = item.value.split('-');
              const tax = strArr?.length > 0 ? strArr[1] : item.name; // 默认展示字段名称 不是空字符串
              value = tax;
            }
          } else {
            value = String(item.value ?? '');
          }

          const type = item.fieldType === 'TABLE' ? 'table' : 'text';
          await wpsEditorEl.value.replaceControlData(item.name, type, value);
        };
      });

      // 分批执行（每批最多5个，避免页面卡顿）
      await runInBatches(taskFns, 5);

      ElMessage.success('保存成功');
      contentLoading.value = false;
    }
  }
}

// WPS加载完毕回调
async function wpsInitEnd() {
  const controls = await wpsEditorEl.value.getAllControl();
  // 只有是补充协议的时候才会自动刷新一次控件
  if (controls.length <= 0 && contractInfo.value.parentId) {
    const filterData = tableOptions.data.filter((v: any) => !!v.parentId);
    const marks = filterData.map((item: any) => {
      return item.name ?? '';
    });
    for (const mark of marks) {
      await wpsEditorEl.value.setControl(mark, true);
    }
  }
  contentLoading.value = false;
}

async function setCurrentRow(
  value: string,
  key: string = 'id',
  isExpand: boolean = true,
) {
  const activeRow = tableOptions.data.find((v: any) => v[key] === value);
  const $grid = tableRef.value;
  nextTick(() => {
    if (activeRow) {
      $grid.setCurrentRow(activeRow);
      currentItem.value = activeRow;
    }
    $grid.setAllTreeExpand(isExpand);
  });
}
onMounted(() => {
  init();
});
</script>
