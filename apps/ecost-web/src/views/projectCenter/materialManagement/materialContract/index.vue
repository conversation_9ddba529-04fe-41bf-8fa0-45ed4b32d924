<template>
  <Page class="ml-4 mt-4 h-full rounded bg-white">
    <vxe-grid ref="tableRef" v-bind="tableOptions" v-on="gridEvents">
      <template #top>
        <div class="flex items-center justify-between gap-4">
          <div class="flex items-center gap-4">
            <div class="mb-2">
              <ElButton
                class="w-20"
                type="primary"
                size="small"
                @click="addContract"
              >
                新增合同
              </ElButton>
            </div>
            <div class="mb-2">
              <ElButton
                type="primary"
                size="small"
                :disabled="!couldPathContract"
                @click="addExtContract"
              >
                新增补充协议
              </ElButton>
            </div>
          </div>
          <div class="flex items-center gap-4">
            <div class="mb-2">
              <ElButton
                class="w-20"
                type="primary"
                size="small"
                @click="exportExcel"
              >
                导出Excel
              </ElButton>
            </div>
            <div class="mb-2">
              <ElButton
                :disabled="!couldDownload"
                type="primary"
                size="small"
                @click="downloadText"
              >
                下载文本
              </ElButton>
            </div>
          </div>
        </div>
      </template>

      <template #seq="{ $rowIndex }">
        <div>{{ $rowIndex + 1 }}</div>
      </template>
      <template #fulfillmentStatus="{ row }">
        <div
          :class="{
            'text-gray-500':
              row.fulfillmentStatus === FulfillmentStatus.COMPLETED,
            'text-green-500':
              row.fulfillmentStatus === FulfillmentStatus.IN_PROGRESS,
            'text-orange-500':
              row.fulfillmentStatus === FulfillmentStatus.NOT_STARTED,
          }"
        >
          {{ getFulfillmentStatusLabel(row.fulfillmentStatus) }}
        </div>
      </template>
      <template #name="{ row }">
        <div :class="row.parentId ? 'pl-6' : 'pl-3'">
          <ElButton
            size="small"
            type="primary"
            link
            @click="openContract({ row })"
          >
            {{
              `${row.name}(${row.partyBName}${dayjs(row.createAt).format('YYYYMM')})`
            }}
          </ElButton>
        </div>
      </template>
      <template #createAt="{ row }">
        <div>
          {{ dayjs(row.createAt).format('YYYY-MM-DD HH:mm:ss') }}
        </div>
      </template>
      <template #proposedStatus="{ row }">
        <div
          :class="{
            'text-green-500': row.proposedStatus === ProposedStatus.OFFICIAL,
            'text-red-500': row.proposedStatus === ProposedStatus.PROVISIONAL,
          }"
        >
          {{ getProposedStatusLabel(row.proposedStatus) }}
        </div>
      </template>
      <template #submitStatus="{ row }">
        <div
          :class="{
            'text-green-500': row.submitStatus === SubmitStatus.SUBMITTED,
            'text-red-500': row.submitStatus === SubmitStatus.PENDING,
          }"
        >
          {{ getSubmitStatusLabel(row.submitStatus) }}
        </div>
      </template>
      <template #auditStatus="{ row }">
        <div
          :class="{
            'text-red-500': row.auditStatus === AuditStatus.PENDING,
            'text-orange-500':
              row.auditStatus === AuditStatus.AUDITING ||
              row.auditStatus === AuditStatus.REJECTED,
            'text-green-500': row.auditStatus === AuditStatus.APPROVED,
          }"
        >
          {{ getAuditStatusLabel(row.auditStatus) }}
        </div>
      </template>
      <template #qrcode="{ row }">
        <ElPopover
          placement="left"
          trigger="click"
          v-if="row.submitStatus === SubmitStatus.SUBMITTED"
        >
          <div class="">
            <QrcodeVue :value="row.name + row.updateTime" :size="120" />
          </div>
          <template #reference>
            <div class="flex justify-center">
              <QrcodeVue :value="row.name" :size="28" />
            </div>
          </template>
        </ElPopover>
      </template>
    </vxe-grid>

    <!-- 弹窗表单 -->
    <AddOrEditContract
      v-model:visible="addFromDialogVisible"
      :form-data="addOrEditContractForm"
      @refresh="refreshData"
    />
    <!-- 子集列表 [合同文本] [货物清单 [附加费] [附件] -->
    <EditDrawer
      ref="EditDrawerEl"
      v-model:visible="drawerVisible"
      v-model:tab-idx="tabIdx"
      :contract-form-data="contractFormData"
      :contract-info-data="contractInfoData"
      :editable="drawerEditable"
      :form-data="drawerForm"
      @refresh="refreshData"
      @move="contractSelectMove"
    />
  </Page>
</template>

<script lang="ts" setup>
import type { addOrEditFormType } from './components/AddOrEditContract.vue';
import type { TabType } from './components/EditDrawer.vue';

import { onBeforeMount, reactive, ref } from 'vue';

import { Page } from '@vben/common-ui';
import { downloadFileFromBlobPart } from '@vben/utils';

import dayjs from 'dayjs';
import { ElButton, ElMessage, ElMessageBox, ElPopover } from 'element-plus';
import QrcodeVue from 'qrcode.vue';

import { getFileCloudUrl } from '#/api/couldApi';
import {
  changeReviewStatus,
  delContractCompilation,
  exportContractCompilation,
  getContractCompilationList,
} from '#/api/enterpriseCenter/materialManagement/materialContract';
import { downloadUrlFile } from '#/utils';
import { amountFormat } from '#/utils/vxeTool';

import AddOrEditContract from './components/AddOrEditContract.vue';
import EditDrawer from './components/EditDrawer.vue';

defineOptions({
  name: 'MaterialContract',
});

// 履约状态
const FulfillmentStatus = {
  COMPLETED: 'COMPLETED', // 已完成
  IN_PROGRESS: 'IN_PROGRESS', // 履约中
  NOT_STARTED: 'NOT_STARTED', // 未开始
} as const;
type FulfillmentStatusType =
  (typeof FulfillmentStatus)[keyof typeof FulfillmentStatus];

function getFulfillmentStatusLabel(status: FulfillmentStatusType) {
  const map = {
    [FulfillmentStatus.NOT_STARTED]: '未开始',
    [FulfillmentStatus.IN_PROGRESS]: '履约中',
    [FulfillmentStatus.COMPLETED]: '已完成',
  };
  return map[status] || '未知状态';
}
// 拟定状态
const ProposedStatus = {
  OFFICIAL: 'OFFICIAL', // 正式合同
  PROVISIONAL: 'PROVISIONAL', // 暂存合同
} as const;
type ProposedStatusType = (typeof ProposedStatus)[keyof typeof ProposedStatus];
function getProposedStatusLabel(status: ProposedStatusType) {
  const map = {
    [ProposedStatus.OFFICIAL]: '正式合同',
    [ProposedStatus.PROVISIONAL]: '暂存合同',
  };
  return map[status] || '未知状态';
}
// 提交状态
const SubmitStatus = {
  PENDING: 'PENDING', // 未提交
  SUBMITTED: 'SUBMITTED', // 已提交
} as const;
type SubmitStatusType = (typeof SubmitStatus)[keyof typeof SubmitStatus];

function getSubmitStatusLabel(status: SubmitStatusType) {
  const map = {
    [SubmitStatus.PENDING]: '未提交',
    [SubmitStatus.SUBMITTED]: '已提交',
  };
  return map[status] || '未知状态';
}

// 审核状态
const AuditStatus = {
  PENDING: 'PENDING', // 待审核
  AUDITING: 'AUDITING', // 审批中
  APPROVED: 'APPROVED', // 审核通过
  REJECTED: 'REJECTED', // 审核拒绝
} as const;
type AuditStatusType = (typeof AuditStatus)[keyof typeof AuditStatus];
function getAuditStatusLabel(status: AuditStatusType) {
  const map = {
    [AuditStatus.PENDING]: '待审核',
    [AuditStatus.AUDITING]: '审批中',
    [AuditStatus.APPROVED]: '已审批',
    [AuditStatus.REJECTED]: '被退回',
  };
  return map[status] || '未知状态';
}
const asuditStatusOption = [
  { label: '待审批', value: AuditStatus.PENDING },
  { label: '审核中', value: AuditStatus.AUDITING },
  { label: '审批通过', value: AuditStatus.APPROVED },
  { label: '驳回', value: AuditStatus.REJECTED },
];
// 当前展示的子集
const tabIdx = ref<TabType>('contract');
// 新增弹窗是否展示
const addFromDialogVisible = ref(false);
// 抽屉弹窗dom
const EditDrawerEl = ref();
// 合同编制侧抽屉是否展示
const drawerVisible = ref(false);
// 是否可编辑
const drawerEditable = ref(true);
// 是否可增加补充协议
const couldPathContract = ref(false);
// 是否可以下载
const couldDownload = ref(false);

const tableRef = ref();
const drawerForm = ref({});
const addOrEditContractForm = ref<addOrEditFormType>({
  // 新增/编辑表单
  id: null,
  parentId: null,

  name: '',
  code: '',
  partyA: '',
  partyB: '',
  contractTemplateId: '',
  partyBType: '',
  proposedStatus: '',
});

const contractFormData = ref();
const contractInfoData = ref({
  contractId: '',
  contractTemplateType: '',
  submitStatus: '',
  parentId: null,
});

interface CurrentItemType extends addOrEditFormType {
  fileKey: string;
  fileName: string;
  fileExt: string;
  fileSize: string;
  createAt: string;
}
// 当前点击项
const currentItem = ref<CurrentItemType>({
  id: '',
  parentId: null,

  name: '',
  code: '',
  partyA: '',
  partyB: '',
  contractTemplateId: '',
  partyBType: '',
  proposedStatus: '',

  fileKey: '',
  fileName: '',
  fileExt: '',
  fileSize: '',
  createAt: '',
});

// 表格配置
const columns = [
  {
    file: 'seq',
    title: '',
    width: '60',
    fixed: 'left',
    slots: {
      default: 'seq',
    },
  },
  {
    field: 'fulfillmentStatus',
    title: '履约状态',
    fixed: 'left',
    width: '80',
    slots: {
      default: 'fulfillmentStatus',
    },
  },
  {
    field: 'name',
    title: '合同名称',
    minWidth: '280',
    fixed: 'left',
    slots: {
      default: 'name',
    },
    treeNode: true,
  },
  {
    field: 'code',
    title: '合同编码',
    width: '150',
  },
  {
    field: 'partyBName',
    title: '供应商简称',
    width: '200',
  },
  {
    field: 'partyAName',
    title: '签约主体',
    width: '200',
  },
  {
    field: 'priceType',
    title: '价格类型',
    width: '150',
  },
  {
    field: 'amount',
    title: '暂定合同金额',
    width: '150',
    formatter: amountFormat,
  },
  {
    field: 'contractTemplateName',
    title: '合同范本',
    // title: '合同编制',
    width: '150',
  },
  {
    field: 'creator',
    title: '编制人',
    width: '150',
  },
  {
    field: 'createAt',
    title: '编制日期',
    width: '150',
    slots: {
      default: 'createAt',
    },
  },
  {
    field: 'proposedStatus',
    title: '拟定状态',
    width: '150',
    slots: {
      default: 'proposedStatus',
    },
  },
  {
    field: 'submitStatus',
    title: '提交状态',
    width: '150',
    slots: {
      default: 'submitStatus',
    },
  },
  {
    field: 'auditStatus',
    title: '审批状态',
    width: '150',
    editRender: {
      name: 'VxeSelect',
      options: asuditStatusOption,
    },
    slots: {
      default: 'auditStatus',
    },
  },
  {
    title: '二维码',
    width: '150',
    slots: {
      default: 'qrcode',
    },
  },
];
const tableOptions = reactive<any>({
  size: 'mini',
  height: '100%',
  border: true,
  align: 'center',
  showOverflow: true,
  keepSource: true,
  headerRowClassName: 'bg-sky-200 text-black-800',
  loading: true,
  rowClassName: ({ row }: any) => {
    return row.parentId ? 'bg-gray-50' : '';
  },
  columnConfig: {
    resizable: true,
  },
  mouseConfig: {
    selected: true,
  },
  treeConfig: {
    rowField: 'id',
    parentField: 'parentId',
    transform: true,
    expandAll: true,
  },
  rowConfig: {
    isCurrent: true,
  },
  menuConfig: {
    body: {
      options: [
        [
          {
            code: 'EDIT_ROW',
            name: '编辑',
            prefixConfig: { icon: 'vxe-icon-edit' },
            disabled: false,
          },
          {
            code: 'DELETE_ROW',
            name: '删除',
            prefixConfig: { icon: 'vxe-icon-delete-fill' },
            disabled: false,
          },
        ],
      ],
    },
    visibleMethod: ({ options }: any) => {
      // if (!row.parentId) {
      //   options[0].forEach((item: any) => {
      //     item.disabled = true;
      //   });
      //   return true;
      // }
      options[0].forEach((item: any) => {
        switch (item.code) {
          default: {
            item.disabled = false;
          }
        }
      });
      return true;
    },
  },
  columns,
  data: [],
  editConfig: {
    trigger: 'dblclick',
    mode: 'cell',
    showStatus: true,
    showIcon: true,
    beforeEditMethod({ row }: any) {
      // 版本数据已启用无法进行编辑
      if (row.submitStatus === SubmitStatus.PENDING) {
        ElMessage.warning('请先提交合同编制');
        return false;
      }
      return true;
    },
  },
});
// 表格事件
const gridEvents = {
  cellClick({ row }: { row: any }) {
    couldPathContract.value = !row.parentId;
    currentItem.value = row;
    couldDownload.value = true;
    drawerEditable.value = row.submitStatus === SubmitStatus.PENDING;
  },
  cellMenu({ row }: { row: any }) {
    couldPathContract.value = !row.parentId;
    const $grid = tableRef.value;
    if ($grid) {
      $grid.setCurrentRow(row);
    }
    currentItem.value = row;

    drawerEditable.value = row.submitStatus === SubmitStatus.PENDING;
  },
  async menuClick({ menu, row }: { menu: any; row: any }) {
    if (menu.code === 'DELETE_ROW') {
      const { id } = row;
      ElMessageBox.confirm('你确定要删除吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'error',
      }).then(async () => {
        const res = await delContractCompilation(id);
        if (res) {
          refreshData();
          ElMessage.success('删除成功');
        }
      });
    }

    if (menu.code === 'EDIT_ROW') {
      const {
        id,
        parentId,
        name,
        code,
        partyA,
        partyB,
        contractTemplateId,
        partyBType,
        proposedStatus,
      } = row;
      addOrEditContractForm.value = row.parentId
        ? {
            id,
            parentId,
            name,
            code,
            contractTemplateId,
          }
        : {
            id,
            parentId,
            name,
            code,
            partyA,
            partyB,
            contractTemplateId,
            partyBType,
            proposedStatus,
          };

      addFromDialogVisible.value = true;
    }
  },
  // 完成编辑
  async editClosed({ row, column }: any) {
    if (column.field === 'auditStatus') {
      const id = row.id;
      const item = asuditStatusOption.find(
        (item) => item.value === row.auditStatus,
      );
      ElMessageBox.confirm(`你确定要${item?.label}吗?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(async () => {
          const params = {
            auditStatus: row.auditStatus,
          };
          const res = await changeReviewStatus(id, params);
          if (res) {
            refreshData();
            ElMessage.success(`${item?.label}成功`);
          }
        })
        .catch(async () => {
          refreshData();
        });
    }
  },
};
// 新增合同
const addContract = () => {
  addFromDialogVisible.value = true;
  // 清空表单数据
  addOrEditContractForm.value = {
    id: null,
    parentId: null,

    name: '',
    code: '',
    partyA: '',
    partyB: '',
    contractTemplateId: '',
    partyBType: '',
    proposedStatus: '',
  };
};
// 新增补充协议
async function addExtContract() {
  const { id } = currentItem.value;

  addOrEditContractForm.value = {
    parentId: id,
    name: '',
    code: '',
    partyA: currentItem.value.partyA,
    partyB: currentItem.value.partyB,
    partyBType: currentItem.value.partyBType,
    proposedStatus: currentItem.value.proposedStatus,
    contractTemplateId: currentItem.value.contractTemplateId,
  };
  addFromDialogVisible.value = true;
}
// 打开合同
async function openContract({ row }: any) {
  tabIdx.value = 'contract';
  contractFormData.value = {
    id: row.id,
    fileName: row.fileName,
    fileKey: row.fileKey,
    fileExt: row.fileExt,
    fileContentType: row.fileContentType,
    createTime: dayjs(row.createAt).unix(),
    updateTime: dayjs(row.createAt).unix(),
  };

  contractInfoData.value = {
    contractId: row.id,
    contractTemplateType: row.contractTemplateType,
    submitStatus: row.submitStatus,
    parentId: row.parentId,
  };

  drawerVisible.value = true;
}
// 获取合同编制列表
async function getList() {
  tableOptions.loading = true;
  const res = await getContractCompilationList();
  tableOptions.loading = false;
  tableOptions.data = res;
}

// 合同选中移动
async function contractSelectMove(move: number) {
  const $grid = tableRef.value;
  const tableData = $grid.getTableData();
  const currentRow = $grid.getCurrentRecord();
  const targetIdx = $grid.getVTRowIndex(currentRow);
  if (move > 0 && targetIdx < tableData.visibleData.length - 1) {
    // 下一个
    const targetItem = tableData.visibleData[targetIdx + 1];
    gridEvents.cellClick({ row: targetItem });
    $grid.setCurrentRow(targetItem);
    openContract({ row: targetItem });
  }
  if (move < 0 && targetIdx > 0) {
    // 上一个
    const targetItem = tableData.visibleData[targetIdx - 1];
    gridEvents.cellClick({ row: targetItem });
    $grid.setCurrentRow(targetItem);
    openContract({ row: targetItem });
  }
}

// 刷新
async function refreshData() {
  await getList();
  await setCurrentRow();
}

async function setCurrentRow() {
  const activeRow = tableOptions.data.find(
    (v: any) => v.id === currentItem.value.id,
  );
  const $grid = tableRef.value;
  if (activeRow && $grid) {
    $grid.setCurrentRow(activeRow);
    currentItem.value = activeRow;
  }
  tableRef.value.setAllTreeExpand(true);
}

// 导出Excel
async function exportExcel() {
  const res = await exportContractCompilation();

  downloadFileFromBlobPart({ fileName: '物资合同编制.xlsx', source: res });
}

// 下载文本
async function downloadText() {
  const fileKeys = [currentItem.value.fileKey];
  const urlData = await getFileCloudUrl(fileKeys);

  const url = urlData[currentItem.value.fileKey];

  await downloadUrlFile(url, currentItem.value.name);
}

// 初始化
async function init() {
  await getList();
}

// 渲染前
onBeforeMount(async () => {
  init();
});
</script>

<style lang="scss">
.vxe-cell--tree-node,
.vxe-tree-cell {
  padding-left: 0 !important;
}
</style>
