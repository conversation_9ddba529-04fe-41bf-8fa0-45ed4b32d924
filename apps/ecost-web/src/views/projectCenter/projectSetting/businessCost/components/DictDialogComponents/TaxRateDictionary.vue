<template>
  <div class="flex">
    <!-- 版本 -->
    <div class="w-64">
      <BasicTitle class="mb-1" title="版本" />
      <vxe-grid
        v-bind="versionOptions"
        ref="versionRef"
        @cell-click="handleVersionCellClick"
        @checkbox-change="handleVersionCheckBoxChange"
      >
        <template #status="{ row }">
          <ElTag
            size="small"
            :type="row.status === 'ENABLED' ? 'success' : 'warning'"
          >
            {{ row.status === 'ENABLED' ? '已发布' : '已停用' }}
          </ElTag>
        </template>
      </vxe-grid>
    </div>

    <!-- 分类 -->
    <div class="ml-2 mr-2 w-96">
      <BasicTitle class="mb-1" title="分类" />
      <vxe-grid
        v-bind="categoryOptions"
        ref="categoryRef"
        @cell-click="handleCategoryCellClick"
      >
        <template #typeSlot="{ row }">
          {{ materialDict.find((item) => item.value === row.type)?.label }}
        </template>
      </vxe-grid>
    </div>

    <!-- 明细 -->
    <div class="w-32 flex-1">
      <BasicTitle class="mb-1" title="明细" />
      <vxe-grid
        v-bind="detailOptions"
        ref="detailRef"
        @cell-click="handleDetailCellClick"
      >
        <!-- 税率类型  -->
        <template #typeEditor="{ row }">
          <vxe-select v-model="row.type" :options="TaxRateDict" />
        </template>
        <template #type="{ row }">
          {{ TaxRateDict.find((item) => item.value === row.type)?.label }}
        </template>
        <!--  执行时间  -->
        <template #excuteDate="{ row }">
          {{
            row.excuteDate && row.excuteDate != ''
              ? dayjs(row.excuteDate).format('YYYY/MM/DD')
              : ''
          }}
        </template>
        <!--  税率   -->
        <template #taxRate="{ row }">
          <div class="flex justify-center">
            <div class="mr-1">{{ row.taxRate }}</div>
            <ElTooltip placement="top-start" :visible="row.changeLogVisible">
              <template #content>
                <div v-if="row.changeLogDetail">
                  <div v-if="row.changeLogDetail.length > 0">
                    <div
                      class="flex"
                      v-for="(v, i) in row.changeLogDetail"
                      :key="v.id"
                    >
                      <div class="mr-1">
                        {{ dayjs(v.updateAt).format('YYYY年MM月DD日HH点mm分') }}
                      </div>
                      <div>{{ v.opreateUserName }} 修改税率</div>
                      <div>由 {{ v.oldValue }} 修改为 {{ v.newValue }}</div>
                    </div>
                  </div>
                  <div v-else class="flex">暂无变更记录</div>
                </div>
                <div class="flex" v-else>
                  <ElIcon>
                    <Loading />
                  </ElIcon>
                </div>
              </template>
              <div
                @mouseenter="getChangeLogData({ row })"
                @mouseleave="row.changeLogVisible = false"
              >
                <ElIcon>
                  <InfoFilled />
                </ElIcon>
              </div>
            </ElTooltip>
          </div>
        </template>
      </vxe-grid>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineEmits, defineProps, nextTick, reactive, ref, watch } from 'vue';

import { InfoFilled, Loading } from '@element-plus/icons-vue';
import dayjs from 'dayjs';
import { ElIcon, ElMessage, ElTag, ElTooltip } from 'element-plus';
import _ from 'lodash';

import { QueryTaxrateDictionaryChangeLog } from '#/api/enterpriseCenter/enterpriseStandards/taxRateDictRelease';
import {
  CategoryList,
  DetailsList,
  VersionList,
} from '#/api/projectCenter/projectSetting/businessCost';
import BasicTitle from '#/components/BasicTitleBar/index.vue';

import { materialDict } from '../../data';

const props = withDefaults(
  defineProps<{
    dictType: string;
    dictTypeId: string;
  }>(),
  {
    dictTypeId: '',
    dictType: 'MATERIAL_DICTIONARY',
  },
);
const emit = defineEmits<{
  (e: 'componentsEvent', payload: Object): void;
}>();

const TaxRateDict = ref([
  {
    label: '增值税专用发票',
    value: 'EXCLUSIVE_USE',
  },
  {
    label: '增值税普通发票',
    value: 'GENERAL_USE',
  },
]);

const selectCategoryIds = ref([]); // 已选择的分类
const selectDetails = ref([]); // 已选择版本
const selectVersionId = ref(''); // 已选择的版本ID
const isUseVersion = ref(false); // 是否有已使用版本
const originalSelectedVersionId = ref(''); // 原始选中的版本ID，用于判断是否有变化

const currentVersion = ref({});
const versionRef = ref();
const versionOptions = reactive({
  height: 700,
  size: 'mini',
  border: true,
  align: 'center',
  keepSource: true,
  rowConfig: {
    isCurrent: true,
    isHover: true,
  },
  columnConfig: {
    resizable: true,
  },
  checkboxConfig: {
    showHeader: false,
    checkMethod: ({ row }: { row: any }) => {
      // 允许勾选的条件：
      // 1. 状态不为DISABLED，或者
      // 2. 状态为DISABLED且之前被选择过（isUse为true）且当前仍然被勾选
      return (
        row.status !== 'DISABLED' ||
        (row.status === 'DISABLED' && row.isUse && row.checked)
      );
    },
  },
  columns: [
    {
      type: 'checkbox',
      width: 40,
    },
    {
      field: 'name',
      title: '名称',
      showOverflow: 'title',
    },
    {
      field: 'orgName',
      title: '编制单位',
    },
    {
      field: 'status',
      title: '状态',
      slots: {
        default: 'status',
      },
    },
  ],
  data: [],
});
async function handleVersionCellClick({ row }) {
  currentVersion.value = row;
  categoryOptions.data = await CategoryList(props.dictTypeId, row.id);
  await nextTick(() => {
    categoryRef.value.setAllTreeExpand(true);
  });
  // 切换版本数据置空
  selectCategoryIds.value = [];
  selectDetails.value = [];
}

function handleVersionCheckBoxChange({ row, records }: any) {
  // 获取所有当前勾选的版本
  const selectedVersions = records.filter(
    (item: any) => item.isUse || (item.status !== 'DISABLED' && !item.isUse),
  );

  // 如果当前勾选的是已禁用版本，直接处理
  if (row.status === 'DISABLED' && row.isUse) {
    // 已禁用版本被取消勾选，设置checked为false，防止再次勾选
    row.checked = false;
    selectVersionId.value = '';
    emit('componentsEvent', {
      versionId: selectVersionId.value,
      hasChanged: selectVersionId.value !== originalSelectedVersionId.value,
    });
    return;
  }

  // 检查是否已经有其他版本被勾选
  const otherSelectedVersions = selectedVersions.filter(
    (item) => item.id !== row.id,
  );

  if (otherSelectedVersions.length > 0) {
    // 如果已经有其他版本被勾选，不允许勾选新版本
    ElMessage.warning('请先取消之前选择的版本，再选择新的版本！');
    // 取消当前行的勾选
    nextTick(() => {
      versionRef.value.setCheckboxRow(row, false);
    });
    return;
  }

  // 如果勾选的是新版本（非禁用状态），更新选中状态
  if (row.status === 'DISABLED') {
    // 处理其他情况
    selectVersionId.value =
      selectedVersions.length === 1 ? selectedVersions[0].id : '';
  } else {
    // 更新选中的版本ID
    selectVersionId.value = row.id;
  }

  emit('componentsEvent', {
    versionId: selectVersionId.value,
    hasChanged: selectVersionId.value !== originalSelectedVersionId.value,
  });
}
// 分类
const categoryRef = ref();
const categoryOptions = reactive({
  height: 700,
  size: 'mini',
  border: true,
  align: 'center',
  keepSource: true,
  rowConfig: {
    isCurrent: true,
    isHover: true,
    keyField: 'id',
  },
  treeConfig: {
    rowField: 'id',
    parentField: 'parentId',
    transform: true,
    expandAll: true,
  },
  columnConfig: {
    resizable: true,
  },
  columns: [
    {
      treeNode: true,
      field: 'code',
      title: '编码',
    },
    {
      field: 'name',
      title: '名称',
    },
    {
      field: 'remark',
      title: '备注',
    },
  ],
  data: [],
});
async function handleCategoryCellClick({ row }) {
  const dictTypeId = props.dictTypeId;
  const versionId = row.versionId;
  const categoryId = row.id;
  detailOptions.data = await DetailsList(dictTypeId, versionId, categoryId);

  // 切换分类数据置空
  selectDetails.value = [];
}
// 明细
const detailRef = ref();
const detailOptions = reactive({
  height: 700,
  size: 'mini',
  border: true,
  align: 'center',
  keepSource: true,
  rowConfig: {
    isCurrent: true,
    isHover: true,
    keyField: 'id',
  },
  columnConfig: {
    resizable: true,
  },
  columns: [
    {
      width: 100,
      title: '编码',
      field: 'code',
    },
    {
      width: 120,
      field: 'name',
      title: '名称',
    },
    {
      field: 'type',
      title: '发票类型',
      // editRender: {},
      slots: {
        edit: 'typeEditor',
        default: 'type',
      },
    },
    {
      field: 'taxRate',
      title: '税率(请用,隔开)',
      slots: {
        default: 'taxRate',
      },
      // editRender: {
      //   name: 'VxeInput',
      //   props: {
      //     placeholder: '请输入税率',
      //   },
      // },
    },
    {
      field: 'excuteDate',
      title: '执行时间',
      slots: {
        default: 'excuteDate',
      },
    },
    {
      field: 'remark',
      title: '备注',
    },
  ],
  data: [],
});
function handleDetailCellClick() {}

// 变更记录
async function getChangeLogData({ row }: any) {
  // 获取变更记录数据
  const id = row.id;
  row.changeLogDetail = null;
  const res = await QueryTaxrateDictionaryChangeLog(id);
  row.changeLogDetail = res;

  row.changeLogVisible = true;
}

// 版本校验
function versionValid(): boolean {
  // 检查是否选择了版本（通过点击或勾选）
  if (_.isEmpty(currentVersion.value) && !selectVersionId.value) {
    ElMessage.warning('请选择版本！');
    return false;
  }

  // 如果有currentVersion但没有selectVersionId，使用currentVersion的id
  if (!_.isEmpty(currentVersion.value) && !selectVersionId.value) {
    selectVersionId.value = currentVersion.value.id;
  }

  return true;
}

function validateAll(): boolean {
  return versionValid();
}
watch(
  () => props.dictType,
  () => {
    getVersionList(props.dictTypeId);
  },
  {
    immediate: true,
  },
);

// 获取版本
async function getVersionList(dictTypeId: string) {
  versionOptions.data = await VersionList(dictTypeId);

  // 过滤出已使用的版本（包括已停用的）
  const isSelect = versionOptions.data.filter((item) => item.isUse);

  // 是否有已使用的版本
  isUseVersion.value = !_.isEmpty(isSelect);

  if (isUseVersion.value) {
    await nextTick(() => {
      // 勾选所有已使用的版本（包括已停用的）
      versionRef.value.setCheckboxRow(isSelect, true);

      // 为已禁用的版本添加checked标记
      isSelect.forEach((item) => {
        if (item.status === 'DISABLED') {
          item.checked = true;
        }
      });

      // 记录原始选中的版本ID（如果有多个，取第一个）
      originalSelectedVersionId.value = isSelect[0]?.id || '';
      selectVersionId.value = originalSelectedVersionId.value;
    });
  }
}

// 获取当前选中的版本信息
function getSelectedVersionInfo() {
  return {
    versionId: selectVersionId.value,
    originalVersionId: originalSelectedVersionId.value,
    hasChanged: selectVersionId.value !== originalSelectedVersionId.value,
  };
}

defineExpose({
  validateAll,
  getSelectedVersionInfo,
});
</script>

<style scoped lang="scss"></style>
