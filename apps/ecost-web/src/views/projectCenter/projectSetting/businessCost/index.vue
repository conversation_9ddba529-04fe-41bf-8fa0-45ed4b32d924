<template>
  <ColPage v-bind="colPageProps">
    <template #left="{ isCollapsed, expand }">
      <div v-if="isCollapsed" @click="expand">
        <ElTooltip content="点击展开左侧">
          <ElButton circle round type="primary">
            <template #icon>
              <IconifyIcon class="text-2xl" icon="bi:arrow-right" />
            </template>
          </ElButton>
        </ElTooltip>
      </div>
      <!-- 右侧表格区域 -->
      <div
        :style="{ minWidth: '200px', overflow: 'hidden' }"
        v-else
        class="bg-card h-full rounded-lg border p-2 pb-20"
      >
        <vxe-grid
          v-bind="dictListOptions"
          ref="dictListRef"
          @cell-click="handDictListCellClick"
        >
          <template #status_slot="{ row }">
            <ElTag size="small" :type="row.status ? 'success' : 'info'">
              {{ row.status ? '已设置' : '未设置' }}
            </ElTag>
          </template>
        </vxe-grid>
      </div>
    </template>
    <div class="h-full">
      <div class="bg-card mb-1 rounded-lg border p-2">
        <ElButton
          :disabled="!dictCurrent.type"
          class="ml-2"
          size="small"
          type="primary"
          @click="handleShowDictDialog"
        >
          选择{{ dictName }}
        </ElButton>
      </div>
      <div style="height: calc(100vh - 166px)" class="flex rounded-lg">
        <Component
          ref="categoryRef"
          :is="category"
          v-bind="categoryProps"
          @category-event="getCategoryComponentData"
          @category-refresh="categoryRefreshFn"
        />
        <Component
          ref="detailRef"
          :is="detail"
          v-bind="detailProps"
          @detail-refresh="detailRefreshFn"
        />
      </div>
    </div>
    <!--  新增字典  -->
    <DictDialog
      v-if="showDialog"
      v-model:visible="showDialog"
      :dict-type-id="dictCurrent.id"
      :dict-type="dictCurrent.type"
      :title="dialogTitle"
      @refresh="refreshFn"
    />
  </ColPage>
</template>

<script setup lang="ts">
import { computed, reactive, ref } from 'vue';

import { ColPage } from '@vben/common-ui';
import { IconifyIcon } from '@vben/icons';

import { ElButton, ElTag, ElTooltip } from 'element-plus';

import { DictList } from '#/api/projectCenter/projectSetting/businessCost';

import DictDialog from './components/DictDialog.vue';
import BusinessCostSubjectCategory from './components/DictIndexComponents/BusinessCostSubject/category.vue';
import BusinessCostSubjectDetail from './components/DictIndexComponents/BusinessCostSubject/detail.vue';
import CostDictionaryCategory from './components/DictIndexComponents/CostDictionary/category.vue';
import CostDictionaryDetail from './components/DictIndexComponents/CostDictionary/detail.vue';
import MachineryDictReleaseCategory from './components/DictIndexComponents/MachineryDictRelease/category.vue';
import MachineryDictReleaseDetail from './components/DictIndexComponents/MachineryDictRelease/detail.vue';
import MaterialDictionaryCategory from './components/DictIndexComponents/MaterialDictionary/category.vue';
import MaterialDictionaryDetail from './components/DictIndexComponents/MaterialDictionary/detail.vue';
import TaxRateDictionaryCategory from './components/DictIndexComponents/TaxRateDictionary/category.vue';
import TaxRateDictionaryDetail from './components/DictIndexComponents/TaxRateDictionary/detail.vue';
import { colPageProps, DICT_TYPE, dictTypeList } from './data';

const showDialog = ref(false);
// 字典版本
const dictListOptions = reactive({
  size: 'mini',
  border: true,
  keepSource: true,
  align: 'center',
  showOverflow: true,
  columnConfig: {
    resizable: true,
  },
  rowConfig: {
    isCurrent: true,
  },
  columns: [
    {
      field: 'name',
      title: '名称',
    },
    {
      width: '100',
      field: 'status',
      title: '状态',
      slots: {
        default: 'status_slot',
      },
    },
  ],
  data: [],
  proxyConfig: {
    response: {
      result: 'list', // 指定后端返回中列表字段的名称
      total: 'pageInfo.total', // 指定后端返回中总条数字段的名称
    },
    ajax: {
      query: async ({ page }, formValues) => {
        return await DictList();
      },
    },
  },
});
const dictListRef = ref();
const dictCurrent = ref({
  type: 'BUSINESS_COST_SUBJECT',
});
function handDictListCellClick({ row }: { row: any }) {
  dictCurrent.value = row;
}

// 分类
const categoryRef = ref();
const currentCategory = ref({});
const componentCategoryMap = {
  [DICT_TYPE.BUSINESS_COST_SUBJECT]: BusinessCostSubjectCategory,
  [DICT_TYPE.MATERIAL_DICTIONARY]: MaterialDictionaryCategory,
  [DICT_TYPE.MECHANICAL_DICTIONARY]: MachineryDictReleaseCategory,
  [DICT_TYPE.COST_DICTIONARY]: CostDictionaryCategory,
  [DICT_TYPE.TAXRATE_DICTIONARY]: TaxRateDictionaryCategory,
};
const category = computed(() => {
  return componentCategoryMap[dictCurrent.value.type] || null;
});
const categoryProps = computed(() => ({
  dictTypeId: dictCurrent.value.id,
}));
function getCategoryComponentData(data: any) {
  currentCategory.value = data;
  detailRef.value.refreshData();
}
function categoryRefreshFn() {
  dictListRef.value.commitProxy('query');
  categoryRef.value.refreshData();
}

// 明细
const detailRef = ref();
const componentDetailMap = {
  [DICT_TYPE.BUSINESS_COST_SUBJECT]: BusinessCostSubjectDetail,
  [DICT_TYPE.MATERIAL_DICTIONARY]: MaterialDictionaryDetail,
  [DICT_TYPE.MECHANICAL_DICTIONARY]: MachineryDictReleaseDetail,
  [DICT_TYPE.COST_DICTIONARY]: CostDictionaryDetail,
  [DICT_TYPE.TAXRATE_DICTIONARY]: TaxRateDictionaryDetail,
};
const detail = computed(() => {
  return componentDetailMap[dictCurrent.value.type] || null;
});
const detailProps = computed(() => ({
  dictTypeId: dictCurrent.value.id,
  categoryId: currentCategory.value.categoryId,
  versionId: currentCategory.value.versionId,
  quoteId: currentCategory.value.quoteId, // 引用业务成本科目id
}));
function detailRefreshFn() {
  detailRef.value.refreshData();
}

// 获取名称
const dictName = computed(() => {
  switch (dictCurrent.value.type) {
    case DICT_TYPE.BUSINESS_COST_SUBJECT: {
      return '业务成本科目';
    }
    case DICT_TYPE.COST_DICTIONARY: {
      return '费用字典';
    }
    case DICT_TYPE.MATERIAL_DICTIONARY: {
      return '材料字典';
    }
    case DICT_TYPE.MECHANICAL_DICTIONARY: {
      return '机械字典';
    }
    case DICT_TYPE.TAXRATE_DICTIONARY: {
      return '税率字典';
    }
    // No default
  }
});
const dialogTitle = computed(() => {
  return (
    dictTypeList.find((item) => item.value === dictCurrent.value.type)?.label ||
    ''
  );
});
function handleShowDictDialog() {
  showDialog.value = true;
}

// 刷新
function refreshFn() {
  dictListRef.value.commitProxy('query');
  categoryRef.value?.refreshData();
}
</script>

<style scoped lang="scss"></style>
