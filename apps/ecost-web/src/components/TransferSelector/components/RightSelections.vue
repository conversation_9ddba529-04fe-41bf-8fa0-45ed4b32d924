<template>
  <div class="selections h-full w-full">
    <div class="flex h-[30px] items-end justify-between pb-2 text-[14px]">
      <div>确认材料</div>
    </div>
    <div class="h-[calc(100%-30px)] w-full">
      <vxe-grid ref="tableRef" v-bind="tableOptions" v-on="gridEvents">
        <template #top></template>
        <template #seq="{ $rowIndex }">
          <div>{{ $rowIndex + 1 }}</div>
        </template>
      </vxe-grid>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { reactive, ref, watch } from 'vue';

const props = withDefaults(
  defineProps<{
    selectionData: any;
  }>(),
  {
    selectionData: [],
  },
);

const emit = defineEmits<{
  (e: 'change', data: any): void;
}>();

// 表格数据
const tableRef = ref();
// 表格配置
const columns = [
  {
    file: 'seq',
    title: '',
    width: '60',
    slots: {
      default: 'seq',
    },
    treeNode: true,
  },
  {
    field: 'code',
    title: '编码',
    width: '100',
  },
  {
    field: 'name',
    title: '名称',
    width: '100',
  },
  {
    field: 'specificationModel',
    title: '规格型号',
    width: '150',
  },
  {
    field: 'meteringUnit',
    title: '计量单位',
    width: '80',
  },
  {
    field: 'remark',
    title: '备注',
    // width: '80',
  },
];

const tableOptions = reactive<any>({
  size: 'mini',
  height: '100%',
  border: true,
  align: 'center',
  showOverflow: true,
  keepSource: true,
  headerRowClassName: 'bg-sky-200 text-black-800',
  loading: false,
  rowClassName: ({ row }: any) => {
    return row.disabled ? 'bg-gray-100' : '';
  },

  columnConfig: {
    resizable: true,
  },
  mouseConfig: {
    selected: true,
  },
  treeConfig: {
    rowField: 'id',
    parentField: 'parentId',
    transform: true,
    expandAll: true,
  },
  rowConfig: {
    isCurrent: true,
  },
  columns,
  data: [],
});

watch(
  () => props.selectionData,
  (nval) => {
    tableOptions.data = nval;
  },
  {
    deep: true,
    immediate: true,
  },
);
// 表格事件
const gridEvents = {
  cellDblclick({ row }: any) {
    if (!row.disabled) {
      emit('change', row);
    }
  },
};
</script>
